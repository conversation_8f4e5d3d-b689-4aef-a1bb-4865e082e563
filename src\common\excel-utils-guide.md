# Excel工具类使用指南

## 概述

本目录包含了完整的Excel处理工具类，支持模板生成、样式美化、数据验证等功能。

## 核心工具类

### 1. XlsxUtils - 主要工具类

**文件**: `xlsxUtils.ts`

**主要方法**:

```typescript
// 标准模板生成（同步）
static generateTemplate(config: ExcelTemplateConfig): Buffer

// 高级模板生成（异步，推荐）
static async generateTemplateAsync(config: ExcelTemplateConfig): Promise<Buffer>

// 数据解析
static parseExcel(buffer: Buffer, config: ExcelParseConfig): ExcelParseResult
```

**使用示例**:

```typescript
import { XlsxUtils } from './xlsxUtils';
import { HISTORICAL_ELEMENT_TEMPLATE_CONFIG } from '../config/excel-template-configs';

// 生成模板
const buffer = XlsxUtils.generateTemplate(HISTORICAL_ELEMENT_TEMPLATE_CONFIG);

// 异步生成（推荐）
const buffer = await XlsxUtils.generateTemplateAsync(HISTORICAL_ELEMENT_TEMPLATE_CONFIG);
```

### 2. ExcelStyleManager - 样式管理器

**文件**: `excel-style-manager.ts`

**功能**: 管理Excel样式，支持多种主题

**支持的主题**:
- `blue`: 蓝色主题（默认）
- `green`: 绿色主题
- `orange`: 橙色主题
- `red`: 红色主题

**主要方法**:

```typescript
// 创建标题样式
static createTitleStyle(config?: StyleConfig): CellStyle

// 创建表头样式
static createHeaderStyle(config?: StyleConfig, isRequired?: boolean): CellStyle

// 应用样式到工作表
static applyStylesToSheet(sheet: XLSX.WorkSheet, layout: LayoutConfig, config?: StyleConfig): void
```

### 3. ExcelAdvancedGenerator - 高级生成器

**文件**: `excel-advanced-generator.ts`

**功能**: 基于ExcelJS的高级Excel生成器，提供丰富的样式和功能

**特性**:
- 数据验证
- 条件格式
- 专业样式
- 多工作表支持

**使用示例**:

```typescript
import { ExcelAdvancedGenerator } from './excel-advanced-generator';

const buffer = await ExcelAdvancedGenerator.generateAdvancedTemplate(config, {
  theme: 'blue',
  enableDataValidation: true,
  enableConditionalFormatting: true,
  fontSize: 12
});
```

## 配置说明

### ExcelTemplateConfig 接口

```typescript
interface ExcelTemplateConfig {
  title: string;                    // 模板标题
  instructions: string[];           // 填写说明
  headers: ExcelTemplateFieldConfig[]; // 表头配置
  exampleData?: any[];             // 示例数据
  sheetName?: string;              // 工作表名称
  styleConfig?: {                  // 样式配置
    enabled: boolean;              // 是否启用样式
    theme?: 'blue' | 'green' | 'orange' | 'red'; // 主题
    showBorders?: boolean;         // 是否显示边框
    highlightRequired?: boolean;   // 是否高亮必填字段
    useAdvancedGenerator?: boolean; // 是否使用高级生成器
  };
}
```

### ExcelTemplateFieldConfig 接口

```typescript
interface ExcelTemplateFieldConfig {
  label: string;        // 字段标签
  key: string;          // 字段键名
  required: boolean;    // 是否必填
  description?: string; // 字段描述
  width?: number;       // 列宽
  type?: 'string' | 'number' | 'date' | 'boolean'; // 数据类型
  validation?: {        // 验证规则
    min?: number;       // 最小值
    max?: number;       // 最大值
    pattern?: string;   // 正则表达式
    options?: string[]; // 选项列表
  };
}
```

## 使用模式

### 1. 简单模式（无样式）

```typescript
const config: ExcelTemplateConfig = {
  title: '简单模板',
  instructions: ['填写说明'],
  headers: [
    { label: '名称', key: 'name', required: true }
  ],
  styleConfig: { enabled: false }
};

const buffer = XlsxUtils.generateTemplate(config);
```

### 2. 标准模式（基础样式）

```typescript
const config: ExcelTemplateConfig = {
  title: '标准模板',
  instructions: ['填写说明'],
  headers: [
    { label: '名称', key: 'name', required: true }
  ],
  styleConfig: {
    enabled: true,
    theme: 'blue',
    showBorders: true,
    highlightRequired: true
  }
};

const buffer = XlsxUtils.generateTemplate(config);
```

### 3. 高级模式（完整功能）

```typescript
const config: ExcelTemplateConfig = {
  title: '高级模板',
  instructions: ['填写说明'],
  headers: [
    {
      label: '名称',
      key: 'name',
      required: true,
      type: 'string',
      validation: { max: 100 }
    }
  ],
  styleConfig: {
    enabled: true,
    theme: 'blue',
    showBorders: true,
    highlightRequired: true,
    useAdvancedGenerator: true
  }
};

const buffer = await XlsxUtils.generateTemplateAsync(config);
```

## 预定义配置

**文件**: `../config/excel-template-configs.ts`

**可用配置**:
- `HISTORICAL_ELEMENT_TEMPLATE_CONFIG`: 历史要素模板
- `MOUNTAIN_TEMPLATE_CONFIG`: 山塬模板
- `WATER_SYSTEM_TEMPLATE_CONFIG`: 水系模板

**使用示例**:

```typescript
import { HISTORICAL_ELEMENT_TEMPLATE_CONFIG } from '../config/excel-template-configs';

const buffer = XlsxUtils.generateTemplate(HISTORICAL_ELEMENT_TEMPLATE_CONFIG);
```

## 性能建议

1. **使用异步方法**: 对于复杂模板，推荐使用 `generateTemplateAsync`
2. **启用缓存**: 在服务层使用 `ExcelTemplateService` 的缓存功能
3. **控制数据量**: 限制示例数据和说明文字的数量
4. **选择合适模式**: 根据需求选择简单/标准/高级模式

## 错误处理

```typescript
try {
  const buffer = await XlsxUtils.generateTemplateAsync(config);
  // 处理成功
} catch (error) {
  if (error.message.includes('高级生成器需要异步调用')) {
    // 使用异步方法
    const buffer = await XlsxUtils.generateTemplateAsync(config);
  } else {
    // 其他错误处理
    console.error('Excel生成失败:', error.message);
  }
}
```

## 注意事项

1. **内存管理**: 大文件生成时注意内存使用
2. **浏览器兼容**: 生成的Excel文件兼容主流Excel版本
3. **字符编码**: 自动处理中文字符编码
4. **文件大小**: 启用样式会增加文件大小
5. **异步操作**: 高级生成器必须使用异步方法
