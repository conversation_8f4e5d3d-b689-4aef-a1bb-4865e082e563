/**
 * Excel配置文件
 * 支持导入、导出等多种场景的灵活配置
 */
import { ExcelTemplateConfig } from './excel-template-configs';

/**
 * 历史要素字段定义（通用）
 */
const HISTORICAL_ELEMENT_FIELDS = [
  {
    label: '名称',
    key: 'name',
    required: true,
    description: '历史要素名称，必填，最大255字符',
    width: 20,
  },
  {
    label: '编号',
    key: 'code',
    required: true,
    description: '历史要素编号，必填，最大50字符，系统内唯一',
    width: 15,
  },
  {
    label: '类型ID',
    key: 'typeDictId',
    required: false,
    description: '所属类型ID，可选，需要在类型字典中存在',
    width: 10,
  },
  {
    label: '建筑经度',
    key: 'constructionLongitude',
    required: false,
    description: '建筑经度坐标，可选，范围-180到180',
    width: 15,
  },
  {
    label: '建筑纬度',
    key: 'constructionLatitude',
    required: false,
    description: '建筑纬度坐标，可选，范围-90到90',
    width: 15,
  },
  {
    label: '位置描述',
    key: 'locationDescription',
    required: false,
    description: '位置描述信息，可选',
    width: 30,
  },
  {
    label: '建造时间',
    key: 'constructionTime',
    required: false,
    description: '建造时间，可选，格式：YYYY-MM-DD',
    width: 15,
  },
  {
    label: '历史记载',
    key: 'historicalRecords',
    required: false,
    description: '历史文献记载，可选',
    width: 50,
  },
  {
    label: '区域ID',
    key: 'regionDictId',
    required: true,
    description: '所属区域ID，必填，需要在区域字典中存在',
    width: 10,
  },
];

/**
 * 历史要素Excel配置
 */
export const HISTORICAL_ELEMENT_EXCEL_CONFIG = {
  /**
   * 导入模板配置
   */
  IMPORT: {
    title: '历史要素批量导入模板',
    instructions: [
      '请按照要求填写历史要素数据',
      '带*号的字段为必填项',
      '填写完成后保存并上传',
    ],
    headers: HISTORICAL_ELEMENT_FIELDS,
    exampleData: [], // 暂时移除示例数据以避免内存问题
    sheetName: '历史要素数据',
  } as ExcelTemplateConfig,

  /**
   * 导出配置
   */
  EXPORT: {
    title: '历史要素数据导出',
    headers: HISTORICAL_ELEMENT_FIELDS.map(field => ({
      ...field,
      description: undefined, // 导出时不需要字段说明
    })),
    sheetName: '历史要素数据',
  } as ExcelTemplateConfig,

  /**
   * 报表配置
   */
  REPORT: {
    title: '历史要素统计报表',
    headers: [
      { label: '区域名称', key: 'regionName', width: 20 },
      { label: '要素数量', key: 'elementCount', width: 15 },
      { label: '类型分布', key: 'typeDistribution', width: 30 },
      { label: '统计时间', key: 'statisticsTime', width: 20 },
    ],
    sheetName: '统计报表',
  } as ExcelTemplateConfig,
};

/**
 * 用户Excel配置（示例）
 */
export const USER_EXCEL_CONFIG = {
  IMPORT: {
    title: '用户信息批量导入模板',
    instructions: [
      '请按照以下要求填写用户数据：',
      '1. 带*号的字段为必填项',
      '2. 邮箱格式必须正确',
      '3. 手机号格式必须正确',
    ],
    headers: [
      {
        label: '用户名',
        key: 'username',
        required: true,
        description: '用户登录名，必填，最大50字符',
        width: 20,
      },
      {
        label: '邮箱',
        key: 'email',
        required: true,
        description: '用户邮箱，必填，格式：<EMAIL>',
        width: 30,
      },
      {
        label: '手机号',
        key: 'phone',
        required: false,
        description: '手机号码，可选，格式：13800138000',
        width: 15,
      },
      {
        label: '真实姓名',
        key: 'realName',
        required: false,
        description: '用户真实姓名，可选',
        width: 20,
      },
    ],
    exampleData: [
      {
        username: 'zhangsan',
        email: '<EMAIL>',
        phone: '13800138000',
        realName: '张三',
      },
    ],
    sheetName: '用户数据',
  } as ExcelTemplateConfig,

  EXPORT: {
    title: '用户信息导出',
    headers: [
      { label: '用户名', key: 'username', width: 20 },
      { label: '邮箱', key: 'email', width: 30 },
      { label: '真实姓名', key: 'realName', width: 20 },
      { label: '创建时间', key: 'createdAt', width: 20 },
    ],
    sheetName: '用户数据',
  } as ExcelTemplateConfig,
};

/**
 * 配置类型定义
 */
export type ExcelConfigType = 'IMPORT' | 'EXPORT' | 'REPORT';

/**
 * 获取Excel配置的工具函数
 */
export function getExcelConfig(
  module: string,
  type: ExcelConfigType
): ExcelTemplateConfig {
  const configs: Record<string, any> = {
    HISTORICAL_ELEMENT: HISTORICAL_ELEMENT_EXCEL_CONFIG,
    USER: USER_EXCEL_CONFIG,
  };

  const moduleConfig = configs[module];
  if (!moduleConfig) {
    throw new Error(`未找到模块 ${module} 的Excel配置`);
  }

  const typeConfig = moduleConfig[type];
  if (!typeConfig) {
    throw new Error(`未找到模块 ${module} 的 ${type} 配置`);
  }

  return typeConfig;
}

/**
 * 便捷访问方法
 */
export const ExcelConfigs = {
  HistoricalElement: {
    Import: HISTORICAL_ELEMENT_EXCEL_CONFIG.IMPORT,
    Export: HISTORICAL_ELEMENT_EXCEL_CONFIG.EXPORT,
    Report: HISTORICAL_ELEMENT_EXCEL_CONFIG.REPORT,
  },
  User: {
    Import: USER_EXCEL_CONFIG.IMPORT,
    Export: USER_EXCEL_CONFIG.EXPORT,
  },
};
