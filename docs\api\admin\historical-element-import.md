# 历史要素导入 API 文档

## 概述

历史要素导入功能提供完整的 Excel 批量导入解决方案，包括模板下载、数据预览、正式导入等功能。

## API 接口

### 1. 获取导入模板

**GET** `/admin/historical-element/template/download`

获取 Excel 导入模板下载链接。

#### 响应
```json
{
  "downloadUrl": "http://localhost:7001/public/templates/historical_element_import_template.xlsx",
  "filename": "历史要素导入模板.xlsx",
  "description": "点击链接下载Excel导入模板，包含字段说明和示例数据"
}
```

**说明**:
- `downloadUrl`: 完整的下载URL，包含协议、域名和端口
- `filename`: 建议的文件名，用于下载时的文件命名
- `description`: 模板说明信息

#### 示例
```javascript
const response = await fetch('/admin/historical-element/template/download');
const data = await response.json();
window.open(data.downloadUrl);
```

---

### 2. 导入数据预览

**POST** `/admin/historical-element/import/preview`

验证 Excel 文件并预览导入结果，不会实际导入数据到数据库。

#### 请求
- **Content-Type**: `multipart/form-data`
- **文件字段**: `files`
- **支持格式**: `.xlsx`, `.xls`
- **文件大小**: 最大 10MB

#### 响应
```json
{
  "success": true,
  "message": "验证成功",
  "totalRows": 100,
  "validRows": 95,
  "errors": [
    {
      "row": 5,
      "field": "编号",
      "value": "DUP001",
      "message": "编号已存在于数据库中"
    }
  ],
  "previewData": [
    {
      "name": "大雁塔",
      "code": "DYT001",
      "constructionLongitude": 108.9640,
      "constructionLatitude": 34.2180,
      "regionDictId": 1
    }
  ]
}
```

#### 错误响应
```json
{
  "success": false,
  "message": "验证失败",
  "totalRows": 100,
  "validRows": 0,
  "errors": [
    {
      "row": 2,
      "field": "名称",
      "value": "",
      "message": "名称不能为空"
    }
  ]
}
```

#### 示例
```javascript
const formData = new FormData();
formData.append('files', excelFile);

const response = await fetch('/admin/historical-element/import/preview', {
  method: 'POST',
  body: formData
});
const result = await response.json();

if (result.success && result.errors.length === 0) {
  console.log('验证通过，可以导入');
} else {
  console.log('验证失败:', result.errors);
}
```

---

### 3. 正式导入数据

**POST** `/admin/historical-element/import/excel`

解析 Excel 文件并将数据导入到数据库。

#### 请求
同预览接口的请求格式。

#### 响应
```json
{
  "success": true,
  "message": "导入成功",
  "totalRows": 100,
  "validRows": 95,
  "importedCount": 95
}
```

#### 错误响应
```json
{
  "success": false,
  "message": "导入失败: 数据验证错误",
  "totalRows": 100,
  "validRows": 0,
  "errors": [
    {
      "row": 2,
      "field": "编号",
      "value": "DUP001",
      "message": "编号已存在于数据库中"
    }
  ]
}
```

#### 示例
```javascript
const formData = new FormData();
formData.append('files', excelFile);

const response = await fetch('/admin/historical-element/import/excel', {
  method: 'POST',
  body: formData
});
const result = await response.json();

if (result.success) {
  console.log(`导入成功：${result.importedCount} 条记录`);
} else {
  console.log('导入失败:', result.message);
}
```

---

### 4. 批量导入接口

**POST** `/admin/historical-element/batch-import`

直接批量导入历史要素数据，用于程序调用。

#### 请求
```json
{
  "elements": [
    {
      "name": "大雁塔",
      "code": "DYT001",
      "constructionLongitude": 108.9640,
      "constructionLatitude": 34.2180,
      "regionDictId": 1,
      "typeDictId": 1,
      "locationDescription": "位于西安市雁塔区大慈恩寺内",
      "constructionTime": "652-01-01",
      "historicalRecords": "大雁塔又名慈恩寺塔..."
    }
  ]
}
```

#### 响应
```json
{
  "message": "批量导入成功"
}
```

#### 示例
```javascript
const data = {
  elements: [
    {
      name: "大雁塔",
      code: "DYT001",
      constructionLongitude: 108.9640,
      constructionLatitude: 34.2180,
      regionDictId: 1
    }
  ]
};

const response = await fetch('/admin/historical-element/batch-import', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify(data)
});
```

## 数据格式

### 历史要素数据结构
```typescript
interface CreateHistoricalElementDTO {
  name: string;                      // 名称（必填）
  code: string;                      // 编号（必填）
  typeDictId?: number;               // 类型ID（可选）
  constructionLongitude?: number;    // 建筑经度（可选）
  constructionLatitude?: number;     // 建筑纬度（可选）
  locationDescription?: string;      // 位置描述（可选）
  constructionTime?: Date;           // 建造时间（可选）
  historicalRecords?: string;        // 历史记载（可选）
  regionDictId: number;              // 区域ID（必填）
}
```

### 验证错误结构
```typescript
interface ExcelValidationError {
  row: number;               // 错误所在行号
  field: string;             // 错误字段
  value: any;                // 错误值
  message: string;           // 错误描述
}
```

## 导入流程

### 标准流程
1. **下载模板** → 调用模板下载接口
2. **填写数据** → 按照模板格式填写
3. **预览验证** → 上传文件进行预览
4. **确认导入** → 验证通过后正式导入

### 验证规则

#### 文件验证
- 文件格式：.xlsx 或 .xls
- 文件大小：最大 10MB
- 文件结构：符合模板格式

#### 数据验证
- **必填字段**：名称、编号、区域ID
- **可选字段**：类型ID、经度、纬度、位置描述、建造时间、历史记载
- **数据类型**：数字字段必须为有效数字
- **数据范围**：经度 -180~180，纬度 -90~90
- **字段长度**：名称最大255字符，编号最大50字符
- **唯一性**：编号在系统中必须唯一
- **外键约束**：区域ID和类型ID必须在字典表中存在

#### 日期格式
支持以下日期格式：
- `YYYY-MM-DD`（推荐）
- `YYYY/MM/DD`
- `YYYY.MM.DD`

## 错误处理

### 常见错误
1. **文件格式错误**：请上传 .xlsx 或 .xls 格式文件
2. **文件过大**：文件大小不能超过 10MB
3. **编号重复**：编号已存在于数据库中
4. **外键不存在**：区域ID或类型ID不存在
5. **数据格式错误**：经纬度必须为有效数字

### 错误定位
- 每个错误都会标明具体的行号和字段
- 提供详细的错误描述和修改建议
- 支持批量错误显示

## 性能建议

- **推荐批次**：每次导入不超过 1,000 条记录
- **网络环境**：建议在稳定网络环境下操作
- **预览验证**：大批量数据建议先预览验证
- **分批导入**：超大数据集可分批次进行导入
