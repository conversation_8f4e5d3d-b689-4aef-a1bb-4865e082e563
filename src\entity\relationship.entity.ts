import {
  Table,
  Column,
  DataType,
  BelongsTo,
  HasMany,
  ForeignKey,
  Model,
} from 'sequelize-typescript';
import { RelationshipDict } from './relationship-dict.entity';

export interface RelationshipAttributes {
  /** ID */
  id: number;
  /** 关系名称 */
  name: string;
  /** 关系编号 */
  code: string;
  /** 关系类型ID */
  relationDictId?: number;
  /** 父级关系ID */
  parentRelationshipId?: number;
  /** 关联源ID（如山塬ID、水系ID、历史要素ID） */
  sourceId: number;
  /** 关联目标ID（如山塬ID、水系ID、历史要素ID） */
  targetId: number;
}

/**
 * 关系表模型
 */
@Table({
  tableName: 'relationship',
  comment: '关系表',
})
export class Relationship
  extends Model<RelationshipAttributes>
  implements RelationshipAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  })
  id: number;

  @Column({
    type: DataType.STRING(255),
    allowNull: false,
    comment: '关系名称',
  })
  name: string;

  @Column({
    type: DataType.STRING(50),
    allowNull: false,
    comment: '关系编号',
  })
  code: string;

  @ForeignKey(() => RelationshipDict)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '关系类型ID',
    field: 'relation_dict_id',
  })
  relationDictId: number;

  @ForeignKey(() => Relationship)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '父级关系ID',
    field: 'parent_relationship_id',
  })
  parentRelationshipId: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '关联源ID（如山塬ID、水系ID、历史要素ID）',
    field: 'source_id',
  })
  sourceId: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '关联目标ID（如山塬ID、水系ID、历史要素ID）',
    field: 'target_id',
  })
  targetId: number;

  // 关联关系
  @BelongsTo(() => RelationshipDict, 'relationDictId')
  relationDict: RelationshipDict;

  // 自关联关系
  @BelongsTo(() => Relationship, 'parentRelationshipId')
  parent: Relationship;

  @HasMany(() => Relationship, 'parentRelationshipId')
  children: Relationship[];
}
