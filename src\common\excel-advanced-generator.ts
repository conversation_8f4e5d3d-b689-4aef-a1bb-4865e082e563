/**
 * 高级Excel生成器
 * 基于ExcelJS提供更强大的样式和功能支持
 */
import * as ExcelJS from 'exceljs';
import type { ExcelTemplateConfig } from '../config/excel-template-configs';

export interface AdvancedStyleConfig {
  theme?: 'blue' | 'green' | 'orange' | 'red' | 'purple';
  headerHeight?: number;
  dataRowHeight?: number;
  fontSize?: number;
  enableDataValidation?: boolean;
  enableConditionalFormatting?: boolean;
}

/**
 * 高级Excel生成器
 */
export class ExcelAdvancedGenerator {
  private static readonly THEME_COLORS = {
    blue: {
      primary: '4472C4',
      secondary: 'E7E6E6',
      accent: '70AD47',
      text: 'FFFFFF',
      required: 'FF0000',
    },
    green: {
      primary: '70AD47',
      secondary: 'E2EFDA',
      accent: '4472C4',
      text: 'FFFFFF',
      required: 'FF0000',
    },
    orange: {
      primary: 'ED7D31',
      secondary: 'FCE4D6',
      accent: '70AD47',
      text: 'FFFFFF',
      required: 'FF0000',
    },
    red: {
      primary: 'C5504B',
      secondary: 'F2DCDB',
      accent: '4472C4',
      text: 'FFFFFF',
      required: 'FFFFFF',
    },
    purple: {
      primary: '7030A0',
      secondary: 'E4DFEC',
      accent: '4472C4',
      text: 'FFFFFF',
      required: 'FF0000',
    },
  };

  /**
   * 生成高级Excel模板
   */
  static async generateAdvancedTemplate(
    config: ExcelTemplateConfig,
    styleConfig: AdvancedStyleConfig = {}
  ): Promise<Buffer> {
    const workbook = new ExcelJS.Workbook();

    // 设置工作簿属性
    workbook.creator = '智慧影鉴系统';
    workbook.lastModifiedBy = '智慧影鉴系统';
    workbook.created = new Date();
    workbook.modified = new Date();

    // 创建主工作表
    const worksheet = workbook.addWorksheet(config.sheetName || '数据导入', {
      pageSetup: {
        paperSize: 9, // A4
        orientation: 'landscape',
        fitToPage: true,
      },
    });

    // 生成工作表内容
    await this.generateWorksheetContent(worksheet, config, styleConfig);

    // 如果启用了数据验证，添加验证规则
    if (styleConfig.enableDataValidation) {
      this.addDataValidation(worksheet, config);
    }

    // 如果启用了条件格式，添加条件格式
    if (styleConfig.enableConditionalFormatting) {
      this.addConditionalFormatting(worksheet, config, styleConfig);
    }

    // 生成Buffer
    const buffer = await workbook.xlsx.writeBuffer();
    return Buffer.from(buffer);
  }

  /**
   * 生成工作表内容
   */
  private static async generateWorksheetContent(
    worksheet: ExcelJS.Worksheet,
    config: ExcelTemplateConfig,
    styleConfig: AdvancedStyleConfig
  ): Promise<void> {
    const theme = this.THEME_COLORS[styleConfig.theme || 'blue'];
    let currentRow = 1;

    // 1. 添加标题
    const titleCell = worksheet.getCell(currentRow, 1);
    titleCell.value = config.title;
    titleCell.font = {
      size: styleConfig.fontSize ? styleConfig.fontSize + 4 : 16,
      bold: true,
      color: { argb: '000000' },
    };
    titleCell.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: theme.secondary },
    };
    titleCell.alignment = {
      horizontal: 'center',
      vertical: 'middle',
    };
    titleCell.border = this.createBorder();

    // 合并标题行
    worksheet.mergeCells(currentRow, 1, currentRow, config.headers.length);
    worksheet.getRow(currentRow).height = styleConfig.headerHeight || 30;
    currentRow++;

    // 2. 添加空行
    currentRow++;

    // 3. 添加填写说明
    const instructions = config.instructions.slice(0, 5); // 限制说明数量
    for (const instruction of instructions) {
      const cell = worksheet.getCell(currentRow, 1);
      cell.value = instruction;
      cell.font = {
        size: styleConfig.fontSize ? styleConfig.fontSize - 1 : 11,
        color: { argb: '666666' },
      };
      cell.alignment = { horizontal: 'left', vertical: 'middle' };
      worksheet.mergeCells(currentRow, 1, currentRow, config.headers.length);
      currentRow++;
    }

    // 4. 添加空行
    currentRow++;

    // 5. 添加表头
    const headerRow = worksheet.getRow(currentRow);
    headerRow.height = styleConfig.headerHeight || 25;

    config.headers.forEach((header, index) => {
      const cell = headerRow.getCell(index + 1);
      cell.value = header.required ? `${header.label}*` : header.label;

      // 设置表头样式
      cell.font = {
        size: styleConfig.fontSize || 12,
        bold: true,
        color: { argb: header.required ? theme.required : theme.text },
      };
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: theme.primary },
      };
      cell.alignment = {
        horizontal: 'center',
        vertical: 'middle',
      };
      cell.border = this.createBorder();

      // 设置列宽
      worksheet.getColumn(index + 1).width = header.width || 15;
    });
    currentRow++;

    // 6. 添加字段说明行
    const descRow = worksheet.getRow(currentRow);
    descRow.height = 20;

    config.headers.forEach((header, index) => {
      const cell = descRow.getCell(index + 1);
      cell.value = header.description || '';
      cell.font = {
        size: (styleConfig.fontSize || 12) - 2,
        italic: true,
        color: { argb: '666666' },
      };
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'F8F9FA' },
      };
      cell.alignment = {
        horizontal: 'center',
        vertical: 'middle',
      };
      cell.border = this.createBorder();
    });
    currentRow++;

    // 7. 添加示例数据行（空行供用户填写）
    for (let i = 0; i < 3; i++) {
      const dataRow = worksheet.getRow(currentRow);
      dataRow.height = styleConfig.dataRowHeight || 20;

      config.headers.forEach((header, index) => {
        const cell = dataRow.getCell(index + 1);
        cell.value = '';
        cell.border = this.createBorder();
        cell.alignment = {
          horizontal: 'left',
          vertical: 'middle',
        };
      });
      currentRow++;
    }
  }

  /**
   * 创建边框样式
   */
  private static createBorder(): Partial<ExcelJS.Borders> {
    return {
      top: { style: 'thin', color: { argb: 'CCCCCC' } },
      left: { style: 'thin', color: { argb: 'CCCCCC' } },
      bottom: { style: 'thin', color: { argb: 'CCCCCC' } },
      right: { style: 'thin', color: { argb: 'CCCCCC' } },
    };
  }

  /**
   * 添加数据验证
   */
  private static addDataValidation(
    worksheet: ExcelJS.Worksheet,
    config: ExcelTemplateConfig
  ): void {
    config.headers.forEach((header, index) => {
      try {
        if (header.type === 'number' && header.validation) {
          // 数字验证
          worksheet
            .getColumn(index + 1)
            .eachCell({ includeEmpty: true }, (cell, rowNumber) => {
              if (rowNumber >= 8) {
                // 从数据行开始
                cell.dataValidation = {
                  type: 'decimal',
                  operator: 'between',
                  allowBlank: !header.required,
                  formulae: [
                    header.validation!.min?.toString() || '0',
                    header.validation!.max?.toString() || '999999',
                  ],
                  errorTitle: '数据验证错误',
                  error: `请输入${header.validation!.min || 0}到${
                    header.validation!.max || 999999
                  }之间的数字`,
                };
              }
            });
        } else if (header.type === 'string' && header.validation?.max) {
          // 文本长度验证
          worksheet
            .getColumn(index + 1)
            .eachCell({ includeEmpty: true }, (cell, rowNumber) => {
              if (rowNumber >= 8) {
                cell.dataValidation = {
                  type: 'textLength',
                  operator: 'lessThanOrEqual',
                  allowBlank: !header.required,
                  formulae: [header.validation!.max!.toString()],
                  errorTitle: '文本长度错误',
                  error: `文本长度不能超过${header.validation!.max}个字符`,
                };
              }
            });
        } else if (header.validation?.options) {
          // 下拉列表验证
          worksheet
            .getColumn(index + 1)
            .eachCell({ includeEmpty: true }, (cell, rowNumber) => {
              if (rowNumber >= 8) {
                cell.dataValidation = {
                  type: 'list',
                  allowBlank: !header.required,
                  formulae: [`"${header.validation!.options!.join(',')}"`],
                  errorTitle: '选项错误',
                  error: '请从下拉列表中选择有效选项',
                };
              }
            });
        }
      } catch (error) {
        console.warn(`数据验证设置失败 (${header.label}):`, error.message);
      }
    });
  }

  /**
   * 添加条件格式
   */
  private static addConditionalFormatting(
    worksheet: ExcelJS.Worksheet,
    config: ExcelTemplateConfig,
    styleConfig: AdvancedStyleConfig
  ): void {
    const theme = this.THEME_COLORS[styleConfig.theme || 'blue'];
    // 为必填字段添加条件格式（空值高亮）
    config.headers.forEach((header, index) => {
      if (header.required) {
        try {
          const columnLetter = String.fromCharCode(65 + index);
          const range = `${columnLetter}8:${columnLetter}1000`;

          worksheet.addConditionalFormatting({
            ref: range,
            rules: [
              {
                type: 'expression',
                priority: 1,
                formulae: [`ISBLANK(${columnLetter}8)`],
                style: {
                  fill: {
                    type: 'pattern',
                    pattern: 'solid',
                    bgColor: { argb: theme.accent }, // 浅红色背景
                    fgColor: { argb: 'FFCCCC' }, // 浅红色背景
                  },
                },
              },
            ],
          });
        } catch (error) {
          console.warn(`条件格式设置失败 (${header.label}):`, error.message);
        }
      }
    });
  }
}
