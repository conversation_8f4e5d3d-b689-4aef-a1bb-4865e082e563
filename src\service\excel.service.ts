import { Provide, Inject } from '@midwayjs/core';
import { CreateHistoricalElementDTO } from '../dto/entity.dto';
import { ExcelValidationError, ExcelParseResult } from '../common/xlsxUtils';
import { HistoricalElementService } from './historical-element.service';
import { BaseExcelService } from './base-excel.service';
import { ExcelConfigs } from '../config/excel-configs';

@Provide()
export class ExcelService extends BaseExcelService {
  @Inject()
  historicalElementService: HistoricalElementService;

  /**
   * 生成历史要素导入模板
   */
  async generateHistoricalElementTemplate(): Promise<Buffer> {
    return this.generateTemplate(ExcelConfigs.HistoricalElement.Import);
  }

  /**
   * 解析历史要素Excel文件
   */
  async parseHistoricalElementExcel(
    filePath: string
  ): Promise<ExcelParseResult<CreateHistoricalElementDTO>> {
    const result = await this.parseExcel<any>(
      filePath,
      ExcelConfigs.HistoricalElement.Import,
      this.createHistoricalElementValidator()
    );

    // 转换数据类型
    if (result.success && result.data) {
      result.data = this.convertBatchData(result.data, item =>
        this.convertToDTO(item)
      );
    }

    return result as ExcelParseResult<CreateHistoricalElementDTO>;
  }

  /**
   * 创建历史要素专用的验证器
   */
  private createHistoricalElementValidator() {
    return this.createValidatorWithDuplicateCheck(
      ['编号'], // 需要检查唯一性的字段
      this.validateHistoricalElementData.bind(this) // 自定义验证函数
    );
  }

  /**
   * 转换Excel数据为DTO
   */
  private convertToDTO(data: any): CreateHistoricalElementDTO {
    const dto: CreateHistoricalElementDTO = {
      name: this.parseString(data.name),
      code: this.parseString(data.code),
      regionDictId: this.parseNumber(data.regionDictId)!,
    };

    // 可选字段
    if (data.typeDictId && data.typeDictId.toString().trim() !== '') {
      dto.typeDictId = this.parseNumber(data.typeDictId)!;
    }

    if (
      data.constructionLongitude &&
      data.constructionLongitude.toString().trim() !== ''
    ) {
      dto.constructionLongitude = this.parseNumber(data.constructionLongitude)!;
    }

    if (
      data.constructionLatitude &&
      data.constructionLatitude.toString().trim() !== ''
    ) {
      dto.constructionLatitude = this.parseNumber(data.constructionLatitude)!;
    }

    if (
      data.locationDescription &&
      data.locationDescription.toString().trim() !== ''
    ) {
      dto.locationDescription = this.parseString(data.locationDescription);
    }

    if (
      data.constructionTime &&
      data.constructionTime.toString().trim() !== ''
    ) {
      dto.constructionTime = this.parseDate(data.constructionTime)!;
    }

    if (
      data.historicalRecords &&
      data.historicalRecords.toString().trim() !== ''
    ) {
      dto.historicalRecords = this.parseString(data.historicalRecords);
    }

    return dto;
  }

  /**
   * 验证历史要素数据
   */
  private async validateHistoricalElementData(
    data: any,
    rowIndex: number
  ): Promise<ExcelValidationError[]> {
    const errors: ExcelValidationError[] = [];

    // 基础格式验证
    errors.push(
      ...this.validateBasicFields(
        data,
        ExcelConfigs.HistoricalElement.Import,
        rowIndex
      )
    );

    // 历史要素特定的验证
    errors.push(...this.validateHistoricalElementSpecific(data, rowIndex));

    // 如果基础验证通过，进行业务规则验证
    if (errors.length === 0) {
      const businessErrors = await this.validateBusinessRules(data, rowIndex);
      errors.push(...businessErrors);
    }

    return errors;
  }

  /**
   * 历史要素特定的字段验证
   */
  private validateHistoricalElementSpecific(
    data: any,
    rowIndex: number
  ): ExcelValidationError[] {
    const errors: ExcelValidationError[] = [];

    // 验证名称长度
    if (data.name && data.name.toString().length > 255) {
      errors.push({
        row: rowIndex,
        field: '名称',
        value: data.name,
        message: '名称长度不能超过255个字符',
      });
    }

    // 验证编号长度
    if (data.code && data.code.toString().length > 50) {
      errors.push({
        row: rowIndex,
        field: '编号',
        value: data.code,
        message: '编号长度不能超过50个字符',
      });
    }

    // 验证经纬度范围
    const longitude = this.parseNumber(data.constructionLongitude);
    if (longitude !== null && (longitude < -180 || longitude > 180)) {
      errors.push({
        row: rowIndex,
        field: '建筑经度',
        value: longitude,
        message: '建筑经度范围应在-180到180之间',
      });
    }

    const latitude = this.parseNumber(data.constructionLatitude);
    if (latitude !== null && (latitude < -90 || latitude > 90)) {
      errors.push({
        row: rowIndex,
        field: '建筑纬度',
        value: latitude,
        message: '建筑纬度范围应在-90到90之间',
      });
    }

    // 验证建造时间（可选）
    if (
      data.constructionTime &&
      data.constructionTime.toString().trim() !== ''
    ) {
      const constructionDate = this.parseDate(data.constructionTime);
      if (!constructionDate) {
        errors.push({
          row: rowIndex,
          field: '建造时间',
          value: data.constructionTime,
          message: '建造时间格式不正确，请使用YYYY-MM-DD格式',
        });
      } else if (constructionDate > new Date()) {
        errors.push({
          row: rowIndex,
          field: '建造时间',
          value: data.constructionTime,
          message: '建造时间不能晚于当前时间',
        });
      }
    }

    return errors;
  }

  /**
   * 验证业务规则
   */
  private async validateBusinessRules(
    data: any,
    rowIndex: number
  ): Promise<ExcelValidationError[]> {
    const errors: ExcelValidationError[] = [];

    try {
      // 检查编号是否已存在于数据库中
      const existingElement = await this.checkCodeExists(
        data.code.toString().trim()
      );
      if (existingElement) {
        errors.push({
          row: rowIndex,
          field: '编号',
          value: data.code,
          message: '编号已存在于数据库中',
        });
      }

      // 验证区域ID是否存在
      const regionId = this.parseNumber(data.regionDictId);
      if (regionId) {
        const regionExists = await this.checkRegionExists(regionId);
        if (!regionExists) {
          errors.push({
            row: rowIndex,
            field: '区域ID',
            value: regionId,
            message: '区域ID不存在',
          });
        }
      }

      // 验证类型ID是否存在（如果提供了的话）
      if (data.typeDictId && data.typeDictId.toString().trim() !== '') {
        const typeId = this.parseNumber(data.typeDictId);
        if (typeId) {
          const typeExists = await this.checkTypeExists(typeId);
          if (!typeExists) {
            errors.push({
              row: rowIndex,
              field: '类型ID',
              value: typeId,
              message: '类型ID不存在',
            });
          }
        }
      }
    } catch (error) {
      errors.push({
        row: rowIndex,
        field: 'system',
        value: '',
        message: `数据验证失败: ${error.message}`,
      });
    }

    return errors;
  }

  /**
   * 检查编号是否已存在
   */
  private async checkCodeExists(code: string): Promise<boolean> {
    try {
      const existing = await this.historicalElementService.findByCode(code);
      return !!existing;
    } catch (error) {
      console.warn('检查编号存在性失败:', error);
      return false;
    }
  }

  /**
   * 检查区域是否存在
   */
  private async checkRegionExists(regionId: number): Promise<boolean> {
    try {
      const { RegionDict } = await import('../entity');
      const region = await RegionDict.findByPk(regionId);
      return !!region;
    } catch (error) {
      console.warn('检查区域存在性失败:', error);
      return false;
    }
  }

  /**
   * 检查类型是否存在
   */
  private async checkTypeExists(typeId: number): Promise<boolean> {
    try {
      const { TypeDict } = await import('../entity');
      const type = await TypeDict.findByPk(typeId);
      return !!type;
    } catch (error) {
      console.warn('检查类型存在性失败:', error);
      return false;
    }
  }

  /**
   * 生成并保存模板文件到public目录
   */
  async generateTemplateFile(): Promise<string> {
    return super.ensureTemplateExists(
      ExcelConfigs.HistoricalElement.Import,
      'historical_element_import_template.xlsx'
    );
  }

  /**
   * 检查模板文件是否存在，不存在则生成
   */
  async ensureTemplateExists(): Promise<string> {
    return super.ensureTemplateExists(
      ExcelConfigs.HistoricalElement.Import,
      'historical_element_import_template.xlsx'
    );
  }
}
